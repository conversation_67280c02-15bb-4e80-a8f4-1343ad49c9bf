"use client";

import MobileMenu from "../components/MobileMenu";
import JsonLd from "../components/JsonLd";
import SimpleCarousel from "../components/SimpleCarousel";

export default function Home() {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-950 relative overflow-hidden">
      <JsonLd />

      {/* Subtle background pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-[0.03] dark:opacity-[0.05] pointer-events-none"></div>

      {/* Header with solid background */}
      <header className="sticky top-0 z-50 w-full bg-white/90 dark:bg-gray-950/90 backdrop-blur-md border-b border-gray-200 dark:border-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <a href="#" className="flex items-center gap-3 hover:opacity-90 transition-opacity">
              <img
                src="/favicon_white/android-chrome-512x512.png"
                alt="<PERSON>oor Nurzhanov Logo"
                className="w-8 h-8"
              />
              <span className="font-bold text-xl text-gray-900 dark:text-white">Timoor Nurzhanov</span>
            </a>
            <MobileMenu />
            <nav className="hidden sm:flex gap-8 text-gray-600 dark:text-gray-300 font-medium">
              <a href="#about" className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">About</a>
              <a href="#services" className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Services</a>
              <a href="#work" className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Work</a>
              <a href="#contact" className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">Contact</a>
            </nav>
          </div>
        </div>
      </header>

      <div className="pt-8 font-[family-name:var(--font-geist-sans)] relative z-10">

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <section className="py-10 md:py-20 lg:py-28 mb-12 md:mb-24" aria-labelledby="hero-heading">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-12 items-center">
              {/* Left column - Content */}
              <div className="order-2 lg:order-1 px-1">
                <div className="inline-block mb-3 md:mb-4">
                  <span className="text-sm font-semibold px-3 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                    Web Developer & Designer
                  </span>
                </div>

                <h1 id="hero-heading" className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-3 md:mb-6 text-gray-900 dark:text-white leading-tight">
                  High-Converting <span className="text-blue-600 dark:text-blue-400">Business Websites</span>
                </h1>

                <p className="text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 mb-5 md:mb-8">
                  I craft strategic, high-performance websites that help businesses attract more customers, increase conversions, and grow their online presence.
                </p>

                <div className="flex flex-wrap gap-3 md:gap-4 mb-6 md:mb-10">
                  <a href="#contact" className="w-full sm:w-auto px-5 sm:px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm text-center text-base">
                    Get in Touch
                  </a>
                  <a href="#work" className="w-full sm:w-auto px-5 sm:px-6 py-3 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg font-medium transition-colors text-center text-base">
                    View My Work
                  </a>
                </div>

                <div className="flex flex-row flex-wrap gap-4 sm:gap-8">
                  <div className="flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-700 dark:text-gray-300">5+ Years Experience</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-5 h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-700 dark:text-gray-300">50+ Projects Delivered</span>
                  </div>
                </div>
              </div>

              {/* Right column - Image */}
              <div className="order-1 lg:order-2 relative h-[250px] sm:h-[350px] lg:h-[500px] rounded-xl overflow-hidden shadow-xl mb-6 lg:mb-0">
                <img
                  src="/stock/hero2.jpg"
                  alt="Professional web developer workspace"
                  className="w-full h-full object-cover"
                  loading="eager"
                />
                <div className="absolute inset-0 bg-gradient-to-tr from-blue-600/20 to-transparent mix-blend-overlay"></div>
                <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-gray-900/60 to-transparent"></div>
              </div>
            </div>
          </section>

          {/* Services Section */}
          <section id="services" className="py-12 sm:py-16 mb-16 sm:mb-24 bg-gray-50 dark:bg-gray-900 -mx-4 sm:-mx-6 lg:-mx-8 px-4 sm:px-6 lg:px-8" aria-labelledby="services-heading">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-10 sm:mb-16">
                <span className="text-sm font-semibold px-3 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 inline-block mb-3 sm:mb-4">
                  Services
                </span>
                <h2 id="services-heading" className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">
                  Business Website Solutions
                </h2>
                <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                  Comprehensive web services designed to help your business grow online
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-8">
                <div className="bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                    <svg className="w-6 h-6 sm:w-7 sm:h-7 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 dark:text-white">High-Converting Websites</h3>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6">
                    Custom business websites designed with conversion psychology principles that turn visitors into customers. Optimized for search engines and mobile devices.
                  </p>
                  <ul className="space-y-1.5 sm:space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-300">
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Conversion-focused design</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Mobile-responsive layouts</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>SEO optimization</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                    <svg className="w-6 h-6 sm:w-7 sm:h-7 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 dark:text-white">Digital Marketing</h3>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6">
                    Strategic ad campaigns for businesses that target your ideal customers and maximize your return on ad spend. Drive qualified traffic to your business website.
                  </p>
                  <ul className="space-y-1.5 sm:space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-300">
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Google & Meta Ads</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Retargeting campaigns</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Performance tracking</span>
                    </li>
                  </ul>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                    <svg className="w-6 h-6 sm:w-7 sm:h-7 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 dark:text-white">Website Automation</h3>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6">
                    Streamline business operations with custom website workflows, integrations, and automated processes that save hours daily. Increase efficiency and reduce manual tasks.
                  </p>
                  <ul className="space-y-1.5 sm:space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-300">
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Zapier workflows</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>AI chatbots</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>Custom API solutions</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* Portfolio Section */}
          <section id="work" className="py-12 sm:py-16 mb-16 sm:mb-24">
            <div className="text-center mb-8 sm:mb-10">
              <span className="text-sm font-semibold px-3 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 inline-block mb-2 sm:mb-3">
                Portfolio
              </span>
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-3">
                Recent Projects
              </h2>
              <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                A selection of websites I've designed and developed for businesses
              </p>
            </div>

            <div className="mt-8">
              <SimpleCarousel>
              {/* Vlada Sport and Wellness */}
              <div className="group bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700">
                <div className="relative aspect-video overflow-hidden">
                  <img
                    src="/websites/vladasportandwellness2.png"
                    alt="Vlada Sport and Wellness Website"
                    className="w-full h-full object-cover object-top transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <a
                      href="https://www.vladasportandwellness.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm text-sm"
                    >
                      Visit Website
                    </a>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex flex-wrap justify-between items-center gap-2 mb-2">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">Vlada Sport and Wellness</h3>
                    <span className="text-sm px-3 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">Health & Fitness</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-5">
                    A modern, conversion-focused website for a wellness professional that showcases services and facilitates client bookings.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Next.js</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Tailwind CSS</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Vercel</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Booking Integration</span>
                  </div>
                </div>
              </div>

              {/* Plumber Website */}
              <div className="group bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700">
                <div className="relative aspect-video overflow-hidden">
                  <img
                    src="/websites/plumberwebsite1.png"
                    alt="Professional Plumbing Website"
                    className="w-full h-full object-cover object-top transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <a
                      href="https://plumber-website-template-psi.vercel.app/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm text-sm"
                    >
                      Visit Website
                    </a>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex flex-wrap justify-between items-center gap-2 mb-2">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">Professional Plumbing Services</h3>
                    <span className="text-sm px-3 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">Home Services</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-5">
                    A high-converting website for a local plumbing business that generates leads and showcases their professional services.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Next.js</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Tailwind CSS</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Vercel</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Lead Generation</span>
                  </div>
                </div>
              </div>

              {/* Shade Shack AI Chatbot */}
              <div className="group bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700">
                <div className="relative aspect-video overflow-hidden">
                  <img
                    src="/websites/shadeshack1.png"
                    alt="Shade Shack AI Chatbot"
                    className="w-full h-full object-cover object-top transition-transform duration-500 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-gray-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <a
                      href="mailto:<EMAIL>?subject=Request%20Demo%20for%20AI%20Chatbot"
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm text-sm"
                    >
                      Request Demo
                    </a>
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex flex-wrap justify-between items-center gap-2 mb-2">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">Shade Shack AI Chatbot</h3>
                    <span className="text-sm px-3 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">E-commerce</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-5">
                    An AI-powered chatbot for an e-commerce store that recommends and upsells products to customers, enhancing the shopping experience.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Tiledesk</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Make.com</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">AI Integration</span>
                    <span className="text-xs px-2 py-1 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">Automation</span>
                  </div>
                </div>
              </div>
              </SimpleCarousel>
            </div>

            <div className="text-center mt-12">
              <a href="#contact" className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 font-medium hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                Want to see more projects?
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </a>
            </div>
          </section>

          {/* About Me Section */}
          <section id="about" className="py-12 sm:py-16 mb-16 sm:mb-24 bg-gray-50 dark:bg-gray-900 -mx-4 sm:-mx-6 lg:-mx-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-8 sm:mb-10">
                <span className="text-sm font-semibold px-3 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 inline-block mb-2 sm:mb-4">
                  About Me
                </span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">
                  My Background
                </h2>
                <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                  Web developer with a focus on business results
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-12 items-start">
                <div className="lg:col-span-4 mx-auto lg:mx-0 order-1 lg:order-1" style={{ maxWidth: '250px' }}>
                  <div className="relative rounded-xl overflow-hidden shadow-xl mb-5 sm:mb-8">
                    <img
                      src="/timotorosquare.jpg"
                      alt="Timoor Nurzhanov"
                      className="w-full h-auto object-cover"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tr from-blue-600/10 to-transparent mix-blend-overlay"></div>
                  </div>
                </div>

                <div className="lg:col-span-8 order-2 lg:order-2">
                  <p className="text-sm sm:text-base md:text-lg text-gray-600 dark:text-gray-300 mb-3 sm:mb-6">
                    My journey in technology began over 6 years ago at Galway Technical Institute, culminating in a Bachelor's degree from Atlantic Technological University. What sets me apart is my unique blend of technical expertise and business acumen.
                  </p>
                  <p className="text-sm sm:text-base md:text-lg text-gray-600 dark:text-gray-300 mb-3 sm:mb-6">
                    Throughout my career, I've immersed myself in sales psychology and consumer behavior, developing a deep understanding of what drives purchasing decisions. This knowledge allows me to craft websites that not only look beautiful but strategically guide visitors toward conversion.
                  </p>
                  <div className="flex flex-wrap gap-3 md:gap-4 mt-4 sm:mt-8">
                    <a href="#contact" className="w-full sm:w-auto px-5 sm:px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm text-center text-base">
                      Get in Touch
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Conversion Section */}
          <section className="py-12 sm:py-16 mb-16 sm:mb-24">
            <div className="text-center mb-10 sm:mb-16">
              <span className="text-sm font-semibold px-4 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 inline-block mb-4">
                Methodology
              </span>
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">
                High-Converting Websites That Drive Results
              </h2>
              <p className="text-base sm:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Strategic approaches that turn visitors into customers
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
              {/* Strategy Card */}
              <div className="bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-7 h-7 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-lg sm:text-xl font-bold mb-2 sm:mb-3 text-gray-900 dark:text-white">Conversion Strategy</h3>
                <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                  Using proven frameworks like AIDA and PAS to create compelling user journeys that convert visitors into customers.
                </p>
              </div>

              {/* SEO Card */}
              <div className="bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-7 h-7 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-white">SEO Optimization</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Technical excellence and content strategy that helps you rank higher and attract qualified traffic.
                </p>
              </div>

              {/* Psychology Card */}
              <div className="bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                <div className="w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mb-6">
                  <svg className="w-7 h-7 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-900 dark:text-white">Psychology Triggers</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Strategic use of social proof, scarcity, and other psychological triggers to boost conversions.
                </p>
              </div>
            </div>

            {/* Testimonials */}
            <div className="mt-16 py-12 px-8 rounded-xl bg-white dark:bg-gray-800 shadow-md border border-gray-100 dark:border-gray-700">
              <div className="flex flex-col items-center text-center mb-10">
                <span className="text-sm font-semibold px-4 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 inline-block mb-4">
                  Testimonials
                </span>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">What Clients Say</h3>
                <p className="text-gray-600 dark:text-gray-300 max-w-2xl">Don't just take my word for it. Here's what clients have to say about working with me.</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* Testimonial 1 */}
                <div className="bg-gray-50 dark:bg-gray-900 p-6 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center gap-1 text-yellow-400 mb-4">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-6 min-h-[80px]">"Timoor built us a beautiful website that's easy to use and has brought in many new clients. Very happy with the results."</p>
                  <div className="flex items-center gap-3 pt-6">
                    <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 font-semibold">VS</div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Vlada S.</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Vlada Sport and Wellness</p>
                    </div>
                  </div>
                </div>

                {/* Testimonial 2 */}
                <div className="bg-gray-50 dark:bg-gray-900 p-6 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center gap-1 text-yellow-400 mb-4">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-6 min-h-[80px]">"The AI chatbot Timoor created for our store has helped us sell more products. Customers love the personalized recommendations."</p>
                  <div className="flex items-center gap-3 pt-2">
                    <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 font-semibold">SS</div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Sarah S.</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Shade Shack</p>
                    </div>
                  </div>
                </div>

                {/* Testimonial 3 */}
                <div className="bg-gray-50 dark:bg-gray-900 p-6 rounded-xl shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center gap-1 text-yellow-400 mb-4">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-6">"The Google Ads campaign Timoor set up has been a game-changer for our business. We're getting quality leads at half the cost of our previous agency."</p>
                  <div className="flex items-center gap-3 pt-2">
                    <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 font-semibold">JD</div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">James D.</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Digital Marketing Client</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* FAQ Section */}
          <section className="py-12 sm:py-16 mb-16 sm:mb-24 bg-gray-50 dark:bg-gray-900 -mx-4 sm:-mx-6 lg:-mx-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-8 sm:mb-12">
                <span className="text-sm font-semibold px-3 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 inline-block mb-2 sm:mb-4">
                  FAQ
                </span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">
                  Frequently Asked Questions
                </h2>
                <p className="text-sm sm:text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                  Answers to common questions about my services
                </p>
              </div>

              <div className="max-w-3xl mx-auto">
                {/* FAQ Accordion */}
                <div className="space-y-2 sm:space-y-3">
                  {/* Question 1 */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <details className="group">
                      <summary className="flex justify-between items-center font-medium cursor-pointer p-3 sm:p-4 bg-white dark:bg-gray-800">
                        <span className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                          <span className="text-blue-600 dark:text-blue-400 text-lg sm:text-xl leading-none">Q.</span>
                          What types of businesses do you work with?
                        </span>
                        <span className="transition group-open:rotate-180">
                          <svg fill="none" height="20" width="20" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" className="text-gray-400 dark:text-gray-500">
                            <path d="M6 9l6 6 6-6"></path>
                          </svg>
                        </span>
                      </summary>
                      <div className="bg-white dark:bg-gray-800 px-4 pb-4">
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                          I work with businesses of all sizes, from startups to established companies. My specialties include service-based businesses, e-commerce stores, and professional services like consultants and coaches.
                        </p>
                      </div>
                    </details>
                  </div>

                  {/* Question 2 */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <details className="group">
                      <summary className="flex justify-between items-center font-medium cursor-pointer p-3 sm:p-4 bg-white dark:bg-gray-800">
                        <span className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                          <span className="text-blue-600 dark:text-blue-400 text-lg sm:text-xl leading-none">Q.</span>
                          How long does it take to build a website?
                        </span>
                        <span className="transition group-open:rotate-180">
                          <svg fill="none" height="20" width="20" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" className="text-gray-400 dark:text-gray-500">
                            <path d="M6 9l6 6 6-6"></path>
                          </svg>
                        </span>
                      </summary>
                      <div className="bg-white dark:bg-gray-800 px-4 pb-4">
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                          A typical website project takes 1-2 weeks from start to finish. This includes discovery, design, development, and testing phases. For more complex projects with custom functionality, the timeline may extend to 4-8 weeks.
                        </p>
                      </div>
                    </details>
                  </div>

                  {/* Question 3 */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <details className="group">
                      <summary className="flex justify-between items-center font-medium cursor-pointer p-3 sm:p-4 bg-white dark:bg-gray-800">
                        <span className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                          <span className="text-blue-600 dark:text-blue-400 text-lg sm:text-xl leading-none">Q.</span>
                          Do you offer website maintenance?
                        </span>
                        <span className="transition group-open:rotate-180">
                          <svg fill="none" height="20" width="20" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" className="text-gray-400 dark:text-gray-500">
                            <path d="M6 9l6 6 6-6"></path>
                          </svg>
                        </span>
                      </summary>
                      <div className="bg-white dark:bg-gray-800 px-4 pb-4">
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                          Yes, I offer ongoing maintenance packages to keep your website secure, up-to-date, and performing optimally. These packages include regular updates, security monitoring, performance optimization, and content updates as needed.
                        </p>
                      </div>
                    </details>
                  </div>

                  {/* Question 4 */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <details className="group">
                      <summary className="flex justify-between items-center font-medium cursor-pointer p-3 sm:p-4 bg-white dark:bg-gray-800">
                        <span className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                          <span className="text-blue-600 dark:text-blue-400 text-lg sm:text-xl leading-none">Q.</span>
                          What is your pricing structure?
                        </span>
                        <span className="transition group-open:rotate-180">
                          <svg fill="none" height="20" width="20" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" className="text-gray-400 dark:text-gray-500">
                            <path d="M6 9l6 6 6-6"></path>
                          </svg>
                        </span>
                      </summary>
                      <div className="bg-white dark:bg-gray-800 px-4 pb-4">
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                          I offer transparent, value-based pricing tailored to your specific needs. Website projects typically range from €2,000 to €10,000 depending on complexity and features. I provide detailed proposals with clear deliverables and no hidden costs.
                        </p>
                      </div>
                    </details>
                  </div>

                  {/* Question 5 */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <details className="group">
                      <summary className="flex justify-between items-center font-medium cursor-pointer p-3 sm:p-4 bg-white dark:bg-gray-800">
                        <span className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                          <span className="text-blue-600 dark:text-blue-400 text-lg sm:text-xl leading-none">Q.</span>
                          Do you help with content creation?
                        </span>
                        <span className="transition group-open:rotate-180">
                          <svg fill="none" height="20" width="20" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" className="text-gray-400 dark:text-gray-500">
                            <path d="M6 9l6 6 6-6"></path>
                          </svg>
                        </span>
                      </summary>
                      <div className="bg-white dark:bg-gray-800 px-4 pb-4">
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                          Yes, I can assist with content strategy and creation. I offer copywriting services for website pages, blog posts, and marketing materials. I can also help source and optimize images and videos to enhance your website's visual appeal.
                        </p>
                      </div>
                    </details>
                  </div>

                  {/* Question 6 */}
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <details className="group">
                      <summary className="flex justify-between items-center font-medium cursor-pointer p-3 sm:p-4 bg-white dark:bg-gray-800">
                        <span className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                          <span className="text-blue-600 dark:text-blue-400 text-lg sm:text-xl leading-none">Q.</span>
                          What makes your websites different?
                        </span>
                        <span className="transition group-open:rotate-180">
                          <svg fill="none" height="20" width="20" shape-rendering="geometricPrecision" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" viewBox="0 0 24 24" className="text-gray-400 dark:text-gray-500">
                            <path d="M6 9l6 6 6-6"></path>
                          </svg>
                        </span>
                      </summary>
                      <div className="bg-white dark:bg-gray-800 px-4 pb-4">
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
                          My websites are built with conversion in mind from the ground up. I focus on creating beautiful designs that also strategically guide visitors toward taking action. This approach combines aesthetics with psychology and business strategy to maximize results.
                        </p>
                      </div>
                    </details>
                  </div>
                </div>
              </div>

              <div className="text-center mt-12">
                <a href="#contact" className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm">
                  Have more questions?
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </a>
              </div>
            </div>
          </section>

          {/* Contact Section */}
          <section id="contact" className="py-12 sm:py-16 mb-16 sm:mb-20 bg-gray-50 dark:bg-gray-900 -mx-4 sm:-mx-6 lg:-mx-8 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-8 sm:mb-12">
                <span className="text-sm font-semibold px-3 py-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 inline-block mb-2 sm:mb-4">
                  Contact
                </span>
                <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">
                  Get in Touch
                </h2>
                <p className="text-sm sm:text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                  Let's discuss how I can help your business grow online
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-10">
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 sm:p-8 shadow-md border border-gray-100 dark:border-gray-700">
                  <h3 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6 text-gray-900 dark:text-white">Let's Work Together</h3>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6">
                    I'm always open to discussing new projects, creative ideas, or opportunities to be part of your vision. Whether you need a complete website, a redesign, or business automation solutions, I'm here to help.
                  </p>

                  <div className="space-y-4 sm:space-y-6">
                    <div className="flex items-center gap-3 sm:gap-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                        <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                        <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline font-medium text-sm sm:text-base break-all">
                          <EMAIL>
                        </a>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 sm:gap-4">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0">
                        <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">Let's Talk</p>
                        <p className="text-gray-700 dark:text-gray-300 font-medium text-sm sm:text-base">Schedule a free strategy call</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 sm:mt-8">
                    <a
                      href="mailto:<EMAIL>"
                      className="w-full sm:w-auto inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors shadow-sm text-center"
                    >
                      Send Me a Message
                    </a>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 sm:p-8 shadow-md border border-gray-100 dark:border-gray-700">
                  <h3 className="text-lg sm:text-xl font-bold mb-4 sm:mb-6 text-gray-900 dark:text-white">What Happens Next?</h3>
                  <ol className="space-y-6 sm:space-y-8">
                    <li className="flex gap-3 sm:gap-4">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-blue-600 dark:text-blue-400 font-bold text-sm sm:text-base">1</span>
                      </div>
                      <div>
                        <h4 className="font-bold mb-1 sm:mb-2 text-gray-900 dark:text-white text-base sm:text-lg">We Schedule a Call</h4>
                        <p className="text-gray-600 dark:text-gray-300 text-sm sm:text-base">We'll discuss your business, goals, and how I can help you achieve them.</p>
                      </div>
                    </li>
                    <li className="flex gap-3 sm:gap-4">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-blue-600 dark:text-blue-400 font-bold text-sm sm:text-base">2</span>
                      </div>
                      <div>
                        <h4 className="font-bold mb-1 sm:mb-2 text-gray-900 dark:text-white text-base sm:text-lg">I Create a Proposal</h4>
                        <p className="text-gray-600 dark:text-gray-300 text-sm sm:text-base">Based on our discussion, I'll prepare a detailed proposal with timeline and pricing.</p>
                      </div>
                    </li>
                    <li className="flex gap-3 sm:gap-4">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-blue-600 dark:text-blue-400 font-bold text-sm sm:text-base">3</span>
                      </div>
                      <div>
                        <h4 className="font-bold mb-1 sm:mb-2 text-gray-900 dark:text-white text-base sm:text-lg">We Get to Work</h4>
                        <p className="text-gray-600 dark:text-gray-300 text-sm sm:text-base">Once approved, I'll start creating your high-converting website or solution.</p>
                      </div>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </section>

          {/* Footer */}
          <footer className="text-center py-8 sm:py-10 border-t border-gray-200 dark:border-gray-800 mt-8 sm:mt-10">
            <div className="flex flex-wrap items-center justify-center gap-4 sm:gap-8 mb-4 sm:mb-6">
              <a href="#about" className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-sm font-medium px-2 py-1">About</a>
              <a href="#services" className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-sm font-medium px-2 py-1">Services</a>
              <a href="#work" className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-sm font-medium px-2 py-1">Work</a>
              <a href="#contact" className="text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors text-sm font-medium px-2 py-1">Contact</a>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">&copy; {new Date().getFullYear()} Timoor Nurzhanov. All rights reserved.</p>
          </footer>
        </main>
      </div>
    </div>
  );
}
